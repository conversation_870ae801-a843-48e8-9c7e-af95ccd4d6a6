#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译工具
支持将字幕批量翻译成多种语言
"""

import os
from audio_to_subtitle import AudioToSubtitle


def batch_translate_subtitle(input_file: str, languages: list):
    """
    批量翻译字幕到多种语言
    
    Args:
        input_file: 输入字幕文件
        languages: 目标语言列表
    """
    # API配置
    API_KEY = "sk-9iAGRW07EuhPheIiFU4lIGvkJwyM7Ph7k9Ldp4irTyNQK9Wg"
    BASE_URL = "https://api.bianxieai.com"
    
    # 创建转录器
    transcriber = AudioToSubtitle(API_KEY, BASE_URL)
    
    # 语言名称映射
    language_names = {
        "zh": "中文",
        "en": "英文", 
        "ja": "日语",
        "ko": "韩语",
        "fr": "法语",
        "de": "德语",
        "es": "西班牙语",
        "ru": "俄语",
        "ar": "阿拉伯语",
        "pt": "葡萄牙语",
        "it": "意大利语"
    }
    
    base_name = os.path.splitext(input_file)[0]
    
    print(f"开始批量翻译 {input_file}...")
    print(f"目标语言: {[language_names.get(lang, lang) for lang in languages]}")
    
    results = []
    
    for lang in languages:
        try:
            output_file = f"{base_name}_{lang}.srt"
            lang_name = language_names.get(lang, lang)
            
            print(f"\n正在翻译为{lang_name}...")
            transcriber.translate_subtitle(input_file, output_file, lang, "srt")
            
            results.append({
                "language": lang,
                "name": lang_name,
                "file": output_file,
                "status": "成功"
            })
            
        except Exception as e:
            print(f"翻译为{lang_name}时出错: {e}")
            results.append({
                "language": lang,
                "name": lang_name,
                "file": f"{base_name}_{lang}.srt",
                "status": f"失败: {e}"
            })
    
    # 显示结果
    print(f"\n{'='*50}")
    print("批量翻译结果:")
    print(f"{'='*50}")
    
    for result in results:
        status_icon = "✅" if result["status"] == "成功" else "❌"
        print(f"{status_icon} {result['name']}: {result['file']} - {result['status']}")
    
    return results


def main():
    """主函数"""
    # 检查输入文件
    if not os.path.exists("subtitle.srt"):
        print("错误: 找不到 subtitle.srt 文件")
        print("请先运行 audio_to_subtitle.py 生成字幕文件")
        return
    
    # 定义要翻译的语言
    target_languages = ["zh", "ja", "ko", "fr", "de", "es"]
    
    print("🌍 批量字幕翻译工具")
    print("=" * 30)
    
    # 执行批量翻译
    results = batch_translate_subtitle("subtitle.srt", target_languages)
    
    # 统计结果
    success_count = sum(1 for r in results if r["status"] == "成功")
    total_count = len(results)
    
    print(f"\n📊 翻译统计:")
    print(f"总计: {total_count} 种语言")
    print(f"成功: {success_count} 种语言")
    print(f"失败: {total_count - success_count} 种语言")
    
    if success_count > 0:
        print(f"\n🎉 翻译完成！生成了 {success_count} 个翻译文件")
        print("您现在可以在视频播放器中使用这些字幕文件")


if __name__ == "__main__":
    main()
