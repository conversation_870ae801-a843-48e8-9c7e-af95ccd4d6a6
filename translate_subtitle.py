#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕翻译工具
支持将字幕翻译成多种语言
"""

import os
from audio_to_subtitle import AudioToSubtitle


def main():
    """主函数"""
    # API配置
    API_KEY = "sk-9iAGRW07EuhPheIiFU4lIGvkJwyM7Ph7k9Ldp4irTyNQK9Wg"
    BASE_URL = "https://api.bianxieai.com"
    
    # 创建转录器（用于翻译功能）
    transcriber = AudioToSubtitle(API_KEY, BASE_URL)
    
    # 检查是否有字幕文件
    subtitle_files = [f for f in os.listdir('.') if f.endswith(('.srt', '.txt'))]
    
    if not subtitle_files:
        print("当前目录没有找到字幕文件(.srt 或 .txt)")
        return
    
    print("发现的字幕文件:")
    for i, file in enumerate(subtitle_files, 1):
        print(f"{i}. {file}")
    
    # 支持的语言
    languages = {
        "zh": "中文",
        "en": "英文", 
        "ja": "日语",
        "ko": "韩语",
        "fr": "法语",
        "de": "德语",
        "es": "西班牙语",
        "ru": "俄语"
    }
    
    print(f"\n支持的翻译语言:")
    for code, name in languages.items():
        print(f"- {code}: {name}")
    
    # 翻译示例
    if "subtitle.srt" in subtitle_files:
        print(f"\n开始翻译 subtitle.srt...")
        
        try:
            # 翻译为中文
            print("翻译为中文...")
            transcriber.translate_subtitle("subtitle.srt", "subtitle_zh.srt", "zh", "srt")
            
            # 翻译为日语
            print("翻译为日语...")
            transcriber.translate_subtitle("subtitle.srt", "subtitle_ja.srt", "ja", "srt")
            
            print("\n翻译完成！生成的文件:")
            print("- subtitle_zh.srt (中文)")
            print("- subtitle_ja.srt (日语)")
            
            # 显示翻译结果预览
            print("\n=== 中文翻译预览 ===")
            if os.path.exists("subtitle_zh.srt"):
                with open("subtitle_zh.srt", 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:200] + "..." if len(content) > 200 else content)
            
            print("\n=== 日语翻译预览 ===")
            if os.path.exists("subtitle_ja.srt"):
                with open("subtitle_ja.srt", 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:200] + "..." if len(content) > 200 else content)
                    
        except Exception as e:
            print(f"翻译过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    else:
        print("\n请先生成 subtitle.srt 文件，然后再运行翻译功能")
        print("运行: python audio_to_subtitle.py")


if __name__ == "__main__":
    main()
